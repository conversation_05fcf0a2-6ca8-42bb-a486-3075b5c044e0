{"name": "28977da4-381e8a65", "frames": [{"timestamp": 10000, "objects": [{"category": "car", "id": 0, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 371.191414, "y1": 230.181541, "x2": 413.503386, "y2": 276.019512}}, {"category": "car", "id": 1, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 300.671456, "y1": 219.603548, "x2": 379.41874, "y2": 286.597504}}, {"category": "car", "id": 2, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 461.692023, "y1": 221.954213, "x2": 496.952, "y2": 259.564858}}, {"category": "car", "id": 3, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 547.201273, "y1": 228.686143, "x2": 576.584586, "y2": 247.491465}}, {"category": "car", "id": 4, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 702.345175, "y1": 219.283481, "x2": 728.202493, "y2": 254.543459}}, {"category": "car", "id": 5, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 712.923168, "y1": 218.108149, "x2": 761.111805, "y2": 259.244788}}, {"category": "car", "id": 6, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 749.358478, "y1": 208.70549, "x2": 835.157756, "y2": 269.822785}}, {"category": "car", "id": 7, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 799.813828, "y1": 218.611863, "x2": 905.593761, "y2": 284.430486}}, {"category": "car", "id": 8, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 862.106453, "y1": 217.436527, "x2": 1063.088324, "y2": 320.865797}}, {"category": "car", "id": 9, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 182.764217, "y1": 224.488524, "x2": 319.102798, "y2": 306.761806}}, {"category": "truck", "id": 10, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 567.097976, "y1": 168.072561, "x2": 667.001245, "y2": 283.255156}}, {"category": "car", "id": 11, "attributes": {"occluded": false, "truncated": true, "trafficLightColor": "none"}, "box2d": {"x1": 1222.933561, "y1": 262.099169, "x2": 1279.349528, "y2": 454.853712}}, {"category": "car", "id": 12, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 429.584062, "y1": 232.715854, "x2": 476.597365, "y2": 263.274503}}, {"category": "traffic sign", "id": 13, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 665.024561, "y1": 169.064247, "x2": 676.777886, "y2": 189.044901}}, {"category": "area/drivable", "id": 14, "attributes": {}, "poly2d": [[568.167261, 286.123025, "L"], [368.444268, 521.774636, "L"], [692.444046, 486.337161, "C"], [1041.756305, 530.212129, "C"], [1280.811619, 623.024567, "C"], [1280.811619, 450.899684, "L"], [1215.568686, 417.149708, "L"], [1208.818691, 349.649755, "L"], [1078.881279, 326.024771, "L"], [918.56889, 336.149765, "L"], [699.194041, 258.524817, "L"], [672.194059, 260.212317, "L"], [666.442077, 287.658569, "L"], [568.167261, 286.123025, "L"]]}, {"category": "lane/road curb", "id": 15, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[181.610527, 309.505265, "L"], [0, 352.989476, "L"]]}, {"category": "lane/road curb", "id": 16, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[1047.457899, 315.900002, "L"], [1108.847374, 331.24737, "L"]]}, {"category": "lane/double yellow", "id": 17, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[546.110529, 291.600003, "L"], [282.64737, 534.600003, "L"]]}, {"category": "lane/double yellow", "id": 18, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[556.342108, 296.715791, "L"], [364.500002, 524.368424, "L"]]}]}], "attributes": {"weather": "clear", "scene": "residential", "timeofday": "daytime"}}