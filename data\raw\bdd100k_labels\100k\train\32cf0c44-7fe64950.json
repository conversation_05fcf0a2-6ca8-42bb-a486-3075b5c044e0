{"name": "32cf0c44-7fe64950", "frames": [{"timestamp": 10000, "objects": [{"category": "car", "id": 0, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 466.660012, "y1": 426.731883, "x2": 542.773009, "y2": 480.385306}}, {"category": "truck", "id": 1, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 606.408465, "y1": 406.767818, "x2": 699.990019, "y2": 484.128568}}, {"category": "traffic sign", "id": 2, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 761.129966, "y1": 389.299261, "x2": 793.571571, "y2": 420.493112}}, {"category": "car", "id": 3, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 723.697345, "y1": 426.731883, "x2": 739.918146, "y2": 439.209423}}, {"category": "area/alternative", "id": 4, "attributes": {}, "poly2d": [[1.513276, 546.656781, "L"], [456.810978, 467.279222, "L"], [462.214724, 484.208201, "L"], [0, 621.541273, "L"], [1.513276, 546.656781, "L"]]}, {"category": "area/alternative", "id": 5, "attributes": {}, "poly2d": [[0.015584, 635.020481, "L"], [474.499076, 485.743745, "L"], [557.156196, 507.716847, "L"], [332.502723, 720.3888, "L"], [0, 720.3888, "L"], [0.015584, 635.020481, "L"]]}, {"category": "area/drivable", "id": 6, "attributes": {}, "poly2d": [[593.100752, 485.251501, "L"], [367.010996, 720.170138, "L"], [995.979312, 720.3888, "L"], [717.409006, 489.744568, "L"], [593.100752, 485.251501, "L"]]}, {"category": "lane/road curb", "id": 7, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[1.247754, 526.552206, "L"], [462.91675, 460.421243, "L"]]}, {"category": "lane/road curb", "id": 8, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[542.77301, 449.191455, "L"], [610.151727, 440.457178, "L"]]}, {"category": "lane/road curb", "id": 9, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[1265.222599, 672.539429, "C"], [1081.802755, 598.921941, "C"], [853.463765, 519.065681, "C"], [713.715312, 450.439209, "L"]]}, {"category": "lane/single white", "id": 10, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[1024.406068, 718.706329, "C"], [759.882211, 521.561191, "C"], [680.025954, 459.173487, "C"], [701.237771, 442.952685, "L"]]}, {"category": "lane/single white", "id": 11, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[995.707726, 716.210821, "C"], [742.413656, 519.065681, "C"], [675.034937, 459.173487, "C"], [697.49451, 440.457178, "L"]]}, {"category": "lane/single white", "id": 12, "attributes": {"direction": "parallel", "style": "dashed"}, "poly2d": [[610.151727, 475.394289, "C"], [537.781993, 525.304452, "C"], [454.182472, 633.859054, "C"], [384.308245, 717.458575, "L"]]}, {"category": "lane/single white", "id": 13, "attributes": {"direction": "parallel", "style": "dashed"}, "poly2d": [[608.903973, 469.15552, "C"], [530.295468, 521.561191, "C"], [435.46616, 632.6113, "C"], [356.857657, 718.706329, "L"]]}, {"category": "lane/single white", "id": 14, "attributes": {"direction": "parallel", "style": "dashed"}, "poly2d": [[0, 625.124775, "L"], [464.164504, 481.63306, "L"]]}, {"category": "lane/single white", "id": 15, "attributes": {"direction": "parallel", "style": "dashed"}, "poly2d": [[1.247754, 635.106808, "L"], [469.15552, 486.624077, "L"]]}, {"category": "lane/single white", "id": 16, "attributes": {"direction": "parallel", "style": "dashed"}, "poly2d": [[541.525254, 464.164504, "L"], [607.656219, 449.191455, "L"]]}, {"category": "lane/single yellow", "id": 17, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[543.396886, 451.686962, "L"], [612.023359, 442.328808, "L"]]}, {"category": "lane/single yellow", "id": 18, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[0, 546.516271, "C"], [153.473748, 516.570174, "C"], [310.690757, 491.615093, "C"], [465.412258, 464.164504, "L"]]}]}], "attributes": {"weather": "partly cloudy", "scene": "highway", "timeofday": "daytime"}}