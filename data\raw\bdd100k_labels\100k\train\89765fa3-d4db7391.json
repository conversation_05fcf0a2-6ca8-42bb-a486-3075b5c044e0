{"name": "89765fa3-d4db7391", "frames": [{"timestamp": 10000, "objects": [{"category": "traffic sign", "id": 0, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 101.82855, "y1": 265.283207, "x2": 119.020383, "y2": 286.442386}}, {"category": "traffic light", "id": 1, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "green"}, "box2d": {"x1": 473.436634, "y1": 137.270174, "x2": 491.950916, "y2": 176.943635}}, {"category": "traffic light", "id": 2, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "green"}, "box2d": {"x1": 610.971299, "y1": 159.751803, "x2": 628.163131, "y2": 194.135468}}, {"category": "traffic light", "id": 3, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "green"}, "box2d": {"x1": 775.48393, "y1": 261.580355, "x2": 784.74107, "y2": 281.417086}}, {"category": "traffic light", "id": 4, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "green"}, "box2d": {"x1": 968.296952, "y1": 190.168124, "x2": 982.843888, "y2": 231.164034}}, {"category": "traffic sign", "id": 5, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 975.438168, "y1": 232.486482, "x2": 999.242246, "y2": 261.580353}}, {"category": "traffic sign", "id": 6, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 608.326401, "y1": 291.996674, "x2": 617.583542, "y2": 302.576263}}, {"category": "traffic light", "id": 7, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "red"}, "box2d": {"x1": 449.632558, "y1": 192.813019, "x2": 465.501942, "y2": 219.261993}}, {"category": "person", "id": 8, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 920.6888, "y1": 311.833407, "x2": 948.460224, "y2": 372.666047}}, {"category": "person", "id": 9, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 776.541891, "y1": 315.800753, "x2": 795.056173, "y2": 351.506869}}, {"category": "person", "id": 10, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 817.273296, "y1": 315.800748, "x2": 841.077374, "y2": 363.408902}}, {"category": "person", "id": 11, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 693.227624, "y1": 319.768099, "x2": 703.807213, "y2": 342.249726}}, {"category": "car", "id": 12, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 56.518155, "y1": 313.68483, "x2": 109.416103, "y2": 338.811355}}, {"category": "car", "id": 13, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 582.852737, "y1": 308.395035, "x2": 604.011917, "y2": 325.586868}}, {"category": "car", "id": 14, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 584.175186, "y1": 316.329727, "x2": 613.269058, "y2": 337.488906}}, {"category": "area/alternative", "id": 15, "attributes": {}, "poly2d": [[621.911301, 335.260433, "L"], [638.802285, 332.189345, "L"], [1280.6437, 514.919081, "L"], [1280.6437, 662.331306, "L"], [1186.991495, 643.904778, "C"], [1093.32331, 610.12281, "C"], [990.441862, 610.12281, "C"], [621.911301, 335.260433, "L"]]}, {"category": "area/drivable", "id": 16, "attributes": {}, "poly2d": [[962.80207, 608.587266, "L"], [726.328293, 610.12281, "C"], [446.859284, 605.516178, "C"], [239.560844, 643.904778, "C"], [578.916069, 342.938153, "L"], [618.840213, 342.938153, "L"], [962.80207, 608.587266, "L"]]}, {"category": "lane/road curb", "id": 17, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[3.944518, 637.874561, "C"], [195.749865, 536.624631, "C"], [543.944147, 379.687239, "C"], [577.694124, 335.812269, "L"]]}, {"category": "lane/single yellow", "id": 18, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[208.131878, 651.374552, "L"], [557.444138, 350.999759, "L"]]}, {"category": "lane/single yellow", "id": 19, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[238.506857, 647.999555, "L"], [557.444138, 356.062255, "L"]]}, {"category": "lane/single white", "id": 20, "attributes": {"direction": "vertical", "style": "solid"}, "poly2d": [[522.006662, 394.874729, "L"], [942.193873, 408.374719, "L"]]}, {"category": "lane/single white", "id": 21, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[918.56889, 411.749717, "L"], [1276.318644, 509.62465, "L"]]}, {"category": "lane/single white", "id": 22, "attributes": {"direction": "parallel", "style": "dashed"}, "poly2d": [[970.881354, 617.624575, "L"], [717.756528, 415.124715, "L"]]}, {"category": "lane/single white", "id": 23, "attributes": {"direction": "parallel", "style": "dashed"}, "poly2d": [[989.443841, 614.249578, "L"], [724.506523, 411.749717, "L"]]}, {"category": "lane/crosswalk", "id": 24, "attributes": {"direction": "vertical", "style": "solid"}, "poly2d": [[532.131655, 384.749735, "L"], [908.443897, 393.18723, "L"]]}, {"category": "lane/crosswalk", "id": 25, "attributes": {"direction": "vertical", "style": "solid"}, "poly2d": [[552.381641, 367.874747, "L"], [835.881446, 377.99974, "L"]]}, {"category": "lane/single white", "id": 26, "attributes": {"direction": "vertical", "style": "dashed"}, "poly2d": [[613.1316, 334.12477, "L"], [657.006569, 364.499749, "L"]]}, {"category": "lane/single white", "id": 27, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[645.194078, 334.12477, "L"], [754.881502, 367.874747, "L"]]}, {"category": "lane/road curb", "id": 28, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[797.068973, 362.812251, "L"], [670.50656, 340.874766, "L"]]}, {"category": "lane/road curb", "id": 29, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[1274.631145, 475.874673, "L"], [894.943906, 388.124733, "L"]]}]}], "attributes": {"weather": "overcast", "scene": "city street", "timeofday": "daytime"}}