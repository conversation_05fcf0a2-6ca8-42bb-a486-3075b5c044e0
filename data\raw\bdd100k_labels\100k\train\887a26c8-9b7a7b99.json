{"name": "887a26c8-9b7a7b99", "frames": [{"timestamp": 10000, "objects": [{"category": "car", "id": 0, "attributes": {"occluded": false, "truncated": true, "trafficLightColor": "none"}, "box2d": {"x1": 0, "y1": 286.060073, "x2": 106.408196, "y2": 391.856038}}, {"category": "car", "id": 1, "attributes": {"occluded": false, "truncated": true, "trafficLightColor": "none"}, "box2d": {"x1": 1043.265763, "y1": 228.753926, "x2": 1277.656141, "y2": 569.284695}}, {"category": "traffic sign", "id": 2, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 657.62938, "y1": 255.673584, "x2": 678.568164, "y2": 282.122577}}, {"category": "traffic sign", "id": 3, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 416.282333, "y1": 315.183815, "x2": 442.731326, "y2": 344.938929}}, {"category": "car", "id": 4, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 570.1227, "y1": 310.775649, "x2": 591.061484, "y2": 329.510352}}, {"category": "car", "id": 5, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 588.857402, "y1": 308.571568, "x2": 621.918644, "y2": 332.816477}}, {"category": "car", "id": 6, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 612.00027, "y1": 312.979733, "x2": 651.673756, "y2": 339.428726}}, {"category": "car", "id": 7, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 700.608992, "y1": 299.755237, "x2": 784.102394, "y2": 405.110388}}, {"category": "car", "id": 8, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 230.037351, "y1": 295.126666, "x2": 344.649649, "y2": 355.738943}}, {"category": "car", "id": 9, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 733.469702, "y1": 288.386605, "x2": 970.408583, "y2": 437.162183}}, {"category": "area/drivable", "id": 10, "attributes": {}, "poly2d": [[515.86039, 356.262864, "L"], [357.797732, 422.274601, "L"], [0, 678.71045, "L"], [179.161094, 642.50875, "C"], [303.75013, 630.152813, "C"], [505.563777, 619.856196, "C"], [589.996015, 593.085, "C"], [693.991822, 603.381613, "C"], [774.305417, 614.70789, "C"], [940.080911, 617.796875, "C"], [1100.708098, 631.182473, "C"], [1279.869192, 654.864686, "C"], [1279.869192, 583.818046, "L"], [1018.335182, 495.267162, "L"], [1039.958072, 445.843411, "L"], [1014.216536, 439.665442, "L"], [773.275755, 440.695104, "L"], [691.932501, 382.004401, "L"], [663.101979, 385.093386, "C"], [628.093489, 366.559479, "C"], [652.805364, 355.233203, "C"], [515.86039, 356.262864, "L"]]}, {"category": "lane/crosswalk", "id": 11, "attributes": {"direction": "vertical", "style": "dashed"}, "poly2d": [[637.266741, 374.672737, "L"], [483.71234, 374.672737, "L"]]}, {"category": "lane/crosswalk", "id": 12, "attributes": {"direction": "vertical", "style": "dashed"}, "poly2d": [[643.408917, 362.388385, "L"], [503.674412, 359.317297, "L"]]}, {"category": "lane/crosswalk", "id": 13, "attributes": {"direction": "vertical", "style": "dashed"}, "poly2d": [[408.470684, 374.672737, "L"], [225.740948, 371.601649, "L"]]}, {"category": "lane/crosswalk", "id": 14, "attributes": {"direction": "vertical", "style": "dashed"}, "poly2d": [[410.006228, 363.923929, "L"], [256.451828, 360.852841, "L"]]}, {"category": "lane/road curb", "id": 15, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[500.603324, 362.388385, "L"], [394.650788, 399.241441, "L"]]}, {"category": "lane/road curb", "id": 16, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[962.80207, 425.345689, "L"], [1045.721446, 449.914393, "L"]]}, {"category": "lane/road curb", "id": 17, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[337.83566, 347.032945, "L"], [655.693269, 347.032945, "L"]]}]}], "attributes": {"weather": "clear", "scene": "residential", "timeofday": "night"}}