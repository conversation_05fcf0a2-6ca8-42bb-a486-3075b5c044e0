{"name": "bf196891-1bf4eedc", "frames": [{"timestamp": 10000, "objects": [{"category": "car", "id": 0, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 840.88557, "y1": 375.863612, "x2": 856.619396, "y2": 400.338453}}, {"category": "car", "id": 1, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 828.64815, "y1": 374.11541, "x2": 847.878381, "y2": 400.338453}}, {"category": "car", "id": 2, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 802.425107, "y1": 368.870801, "x2": 832.144555, "y2": 402.086655}}, {"category": "car", "id": 3, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 786.691282, "y1": 368.870801, "x2": 818.158933, "y2": 405.583061}}, {"category": "car", "id": 4, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 755.22363, "y1": 370.619003, "x2": 798.928701, "y2": 409.079466}}, {"category": "car", "id": 5, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 701.029342, "y1": 361.877988, "x2": 767.461049, "y2": 416.072277}}, {"category": "car", "id": 6, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 692.288328, "y1": 374.11541, "x2": 748.230819, "y2": 419.568683}}, {"category": "car", "id": 7, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 603.129982, "y1": 356.633381, "x2": 712.611159, "y2": 430.557393}}, {"category": "car", "id": 8, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 430.0579, "y1": 353.136975, "x2": 601.381782, "y2": 454.532741}}, {"category": "car", "id": 9, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 283.208861, "y1": 360.129788, "x2": 498.237812, "y2": 480.755784}}, {"category": "car", "id": 10, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 173.072082, "y1": 379.360018, "x2": 316.424715, "y2": 445.791726}}, {"category": "car", "id": 11, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 101.395766, "y1": 395.093844, "x2": 171.32388, "y2": 444.043524}}, {"category": "car", "id": 12, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 993.678488, "y1": 342.997392, "x2": 1030.390748, "y2": 412.925506}}, {"category": "traffic sign", "id": 13, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 503.482421, "y1": 300.690891, "x2": 527.957261, "y2": 318.172919}}, {"category": "traffic sign", "id": 14, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 856.619396, "y1": 241.251992, "x2": 895.079858, "y2": 270.97144}}, {"category": "area/drivable", "id": 15, "attributes": {}, "poly2d": [[248.63185, 489.712158, "L"], [165.944407, 516.712139, "C"], [64.694477, 557.212112, "C"], [0, 558.899611, "C"], [0, 685.462024, "L"], [192.944389, 663.524539, "L"], [177.756899, 628.087062, "C"], [353.256778, 628.087062, "C"], [424.13173, 643.274554, "C"], [709.319032, 629.774562, "C"], [986.068843, 641.587054, "C"], [1266.193651, 671.962034, "C"], [1121.068749, 614.587072, "C"], [716.069029, 491.399658, "C"], [849.381437, 445.837188, "C"], [854.443933, 401.962219, "L"], [393.75675, 496.462154, "L"], [248.63185, 489.712158, "L"]]}, {"category": "lane/road curb", "id": 16, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[285.627164, 479.08973, "C"], [195.030067, 497.516258, "C"], [96.75525, 537.440402, "C"], [0, 562.009106, "L"]]}, {"category": "lane/road curb", "id": 17, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[825.534626, 488.560815, "L"], [1236.128903, 669.497186, "L"]]}]}], "attributes": {"weather": "overcast", "scene": "residential", "timeofday": "daytime"}}